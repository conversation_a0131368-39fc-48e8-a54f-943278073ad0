# Menu Bar Component

A responsive, feature-rich navigation menu component with dropdown mega-menus, tab switching, and search functionality.

## Features

- **Responsive Design**: Adapts to different screen sizes
- **Mega Menu Dropdowns**: Multi-column dropdown menus with rich content
- **Tab Navigation**: Tabbed content within dropdown menus
- **Search Functionality**: Real-time search within menu items
- **Icon Actions**: Configurable icon buttons with custom actions
- **Keyboard Navigation**: ESC key to close menus
- **Theme Support**: CSS custom properties for easy theming
- **Accessibility**: ARIA attributes and keyboard navigation

## Files

- `menu-bar.html` - Complete HTML structure with sample content
- `menu-bar.css` - All styling including responsive design
- `menu-bar.js` - JavaScript functionality and interactions
- `README.md` - This documentation file

## Dependencies

- **Font Awesome 6.4.0+** - For icons
- **Modern Browser** - Supports CSS Grid, Flexbox, and ES6+

## Quick Start

### 1. Include Required Files

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Page</title>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Menu Bar Component CSS -->
    <link rel="stylesheet" href="path/to/menu-bar.css">
</head>
<body>
    <!-- Your menu bar HTML here -->
    
    <!-- Menu Bar Component JavaScript -->
    <script src="path/to/menu-bar.js"></script>
</body>
</html>
```

### 2. Basic HTML Structure

```html
<nav class="menu-bar">
    <div class="menu-container">
        <div class="menu-items-wrapper">
            <div class="menu-item dropdown">
                <button class="menu-button" data-dropdown="your-menu">
                    <span>YOUR MENU</span>
                    <i class="fas fa-chevron-down menu-arrow"></i>
                </button>
                <div class="dropdown-menu multi-column" id="your-menu-menu">
                    <!-- Menu content here -->
                </div>
            </div>
        </div>
        
        <div class="menu-icon-buttons">
            <button class="icon-button" data-action="your-action" title="Your Action">
                <i class="fas fa-cog"></i>
            </button>
        </div>
    </div>
</nav>
```

## Customization

### Adding New Menu Items

1. **Create Menu Button**:
```html
<div class="menu-item dropdown">
    <button class="menu-button" data-dropdown="new-menu">
        <span>NEW MENU</span>
        <i class="fas fa-chevron-down menu-arrow"></i>
    </button>
    <div class="dropdown-menu multi-column" id="new-menu-menu">
        <!-- Content here -->
    </div>
</div>
```

2. **Add Mega Menu Content**:
```html
<div class="mega-menu-header">
    <h2 class="mega-menu-title">NEW MENU</h2>
    <div class="mega-menu-search">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="Search items..." class="mega-menu-search-input">
    </div>
</div>
<div class="mega-menu-content">
    <div class="mega-cards-grid">
        <a href="#link" class="mega-card">
            <div class="mega-card-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="mega-card-content">
                <h4>Item Title</h4>
                <p>Item description</p>
            </div>
        </a>
    </div>
</div>
```

### Adding Tabs

```html
<div class="mega-menu-tabs">
    <button class="mega-menu-tab active" data-tab="tab1">Tab 1</button>
    <button class="mega-menu-tab" data-tab="tab2">Tab 2</button>
</div>

<div class="mega-menu-tab-content active" id="tab1-content">
    <!-- Tab 1 content -->
</div>
<div class="mega-menu-tab-content" id="tab2-content">
    <!-- Tab 2 content -->
</div>
```

### Theme Customization

Override CSS custom properties:

```css
:root {
    --menu-background: #your-color;
    --menu-text: #your-text-color;
    --dropdown-background: #your-dropdown-bg;
    --accent-color: #your-accent-color;
    /* ... other properties */
}
```

## JavaScript API

### Accessing the Component

```javascript
// Component is available globally after initialization
const menuBar = window.menuBarComponent;
```

### Methods

```javascript
// Open a specific menu
menuBar.openMenu('helpdesk');

// Close a specific menu
menuBar.closeMenu('helpdesk');

// Close all menus
menuBar.closeAllDropdowns();

// Switch to a specific tab
menuBar.switchToTab('helpdesk', 'reports');
```

### Events

Listen for icon button actions:

```javascript
document.addEventListener('menuIconAction', (event) => {
    const action = event.detail.action;
    console.log('Icon action:', action);
    
    // Handle the action
    switch (action) {
        case 'dashboard-settings':
            // Your custom logic
            break;
        // ... other actions
    }
});
```

## Browser Support

- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+

## Responsive Breakpoints

- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: < 768px

## Accessibility Features

- ARIA attributes for screen readers
- Keyboard navigation (ESC to close)
- Focus management
- Semantic HTML structure
- High contrast support

## License

This component is provided as-is for educational and development purposes.
