/* Menu Bar Component CSS */

/* CSS Custom Properties for Theme System */
:root {
    /* Primary colors */
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #000000;
    --accent-hover: #333333;
    --background-primary: #ffffff;
    --background-secondary: #ffffff;
    --text-primary: #000000;
    --text-secondary: #666666;
    --text-hint: #999999;
    --border-color: #cccccc;
    --shadow-light: rgba(0, 0, 0, 0.12);
    --shadow-medium: rgba(0, 0, 0, 0.16);
    --shadow-dark: rgba(0, 0, 0, 0.24);

    /* Header/Menu/Dropdowns */
    --header-background: #000000;
    --header-text: #ffffff;
    --menu-background: #000000;
    --menu-text: #ffffff;
    --dropdown-background: #ffffff;
    --dropdown-category-background: #000000;
    --dropdown-category-text: #ffffff;
    --dropdown-item-background: #ffffff;
    --dropdown-item-text: #000000;
    --dropdown-item-hover-background: #f0f0f0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Roboto', sans-serif;
}

body {
    background: #ffffff;
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* Menu Bar Styles */
.menu-bar {
    background: var(--menu-background);
    color: var(--menu-text);
    position: sticky;
    top: 0;
    z-index: 9000;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-light);
    transition: all 0.3s ease;
}

.menu-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 1rem;
    height: 60px;
}

.menu-items-wrapper {
    display: flex;
    align-items: center;
    gap: 0;
    flex: 1;
}

.menu-item {
    position: relative;
    display: inline-block;
}

.menu-button {
    background: transparent;
    border: none;
    color: var(--menu-text);
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    height: 60px;
    border-radius: 0;
}

.menu-button:hover {
    background: var(--accent-hover);
    color: var(--menu-text);
}

.menu-button.active {
    background: var(--accent-color);
    color: var(--menu-text);
}

.menu-arrow {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.menu-item.active .menu-arrow {
    transform: rotate(180deg);
}

/* Dropdown Menu Styles */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--dropdown-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 32px var(--shadow-dark);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 8500;
    min-width: 300px;
    max-height: 80vh;
    overflow-y: auto;
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu.multi-column {
    min-width: 800px;
    max-width: 1000px;
}

/* Mega Menu Styles */
.mega-menu-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--dropdown-category-background);
    color: var(--dropdown-category-text);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.mega-menu-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.mega-menu-search {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--dropdown-background);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem;
    min-width: 250px;
}

.mega-menu-search i {
    color: var(--text-hint);
    margin-right: 0.5rem;
}

.mega-menu-search-input {
    border: none;
    background: transparent;
    color: var(--dropdown-item-text);
    font-size: 0.9rem;
    flex: 1;
    outline: none;
}

.mega-menu-search-clear {
    background: none;
    border: none;
    color: var(--text-hint);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.mega-menu-search-clear:hover {
    background: var(--dropdown-item-hover-background);
    color: var(--dropdown-item-text);
}

.mega-menu-content {
    padding: 1.5rem;
}

.mega-menu-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.mega-menu-tab {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.mega-menu-tab:hover {
    color: var(--text-primary);
    background: var(--dropdown-item-hover-background);
}

.mega-menu-tab.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.mega-menu-tab-content {
    display: none;
}

.mega-menu-tab-content.active {
    display: block;
}

.mega-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.mega-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--dropdown-item-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    text-decoration: none;
    color: var(--dropdown-item-text);
    transition: all 0.3s ease;
}

.mega-card:hover {
    background: var(--dropdown-item-hover-background);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.mega-card-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--accent-color);
    color: var(--menu-text);
    border-radius: 8px;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.mega-card-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dropdown-item-text);
}

.mega-card-content p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Icon Buttons */
.menu-icon-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.icon-button {
    background: transparent;
    border: none;
    color: var(--menu-text);
    padding: 0.75rem;
    font-size: 1rem;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.icon-button:hover {
    background: var(--accent-hover);
    color: var(--menu-text);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dropdown-menu.multi-column {
        min-width: 600px;
        max-width: 800px;
    }
    
    .mega-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .menu-container {
        padding: 0 0.5rem;
    }
    
    .menu-button {
        padding: 1rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .dropdown-menu.multi-column {
        min-width: 300px;
        max-width: 90vw;
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
    }
    
    .dropdown-menu.multi-column.active {
        transform: translateX(-50%) translateY(0);
    }
    
    .mega-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .mega-menu-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .mega-menu-search {
        min-width: 100%;
    }
}
